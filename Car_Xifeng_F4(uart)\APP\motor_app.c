#include "motor_app.h"

Motor_t right_motor;
Motor_t left_motor; 

void Motor_Init(void)
{
  Motor_Create(&right_motor, &htim1, TIM_CHANNEL_2, GPIOE, GPIO_PIN_9, 1);
  Motor_Create(&left_motor, &htim1, TIM_CHANNEL_4, GPIOE, GPIO_PIN_13, 0);
}

/**
 * @brief 设置两路电机PWM都为高电平
 */
void Motor_SetBothPWM_High(void)
{
  Motor_SetPWM_High(&left_motor);  // 设置左电机PWM为高电平(PE14)
  Motor_SetPWM_High(&right_motor); // 设置右电机PWM为高电平(PE11)
}

/**
 * @brief 直接设置PWM为高电平的替代方法
 */
void Motor_SetPWM_High_Direct(void)
{
  // 直接设置TIM1_CH2和TIM1_CH4的PWM占空比为100%
  __HAL_TIM_SET_COMPARE(&htim1, TIM_CHANNEL_2, 99); // PE11 - 右电机PWM
  __HAL_TIM_SET_COMPARE(&htim1, TIM_CHANNEL_4, 99); // PE14 - 左电机PWM

  // 设置方向引脚为低电平（正转方向）
  HAL_GPIO_WritePin(GPIOE, GPIO_PIN_9, GPIO_PIN_RESET);  // 右电机方向
  HAL_GPIO_WritePin(GPIOE, GPIO_PIN_13, GPIO_PIN_RESET); // 左电机方向
}
